This is pdfTeX, Version 3.141592653-2.6-1.40.25 (MiKTeX 24.1) (preloaded format=pdflatex 2025.1.20)  26 JUN 2025 19:30
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./main.tex
(main.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\miktex\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(D:\miktex\tex/latex/base\size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (D:\miktex\tex/latex/base\inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(D:\miktex\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (D:\miktex\tex/generic/babel\babel.sty
Package: babel 2024/01/07 v24.1 The Babel package
\babel@savecnt=\count195
\U@D=\dimen141
\l@unhyphenated=\language79

(D:\miktex\tex/generic/babel\txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count196

*************************************
* Local config file bblopts.cfg used
*
(D:\miktex\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(D:\miktex\tex/generic/babel-french\french.ldf
Language: french 2024-07-25 v3.6c French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language22). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language22). Reported on input line 92.
\FB@stdchar=\count197
Package babel Info: Making : an active character on input line 421.
Package babel Info: Making ; an active character on input line 422.
Package babel Info: Making ! an active character on input line 423.
Package babel Info: Making ? an active character on input line 424.
\FBguill@level=\count198
\FBold@everypar=\toks19
\FB@Mht=\dimen142
\mc@charclass=\count199
\mc@charfam=\count266
\mc@charslot=\count267
\std@mcc=\count268
\dec@mcc=\count269
\FB@parskip=\dimen143
\listindentFB=\dimen144
\descindentFB=\dimen145
\labelindentFB=\dimen146
\labelwidthFB=\dimen147
\leftmarginFB=\dimen148
\parindentFFN=\dimen149
\FBfnindent=\dimen150
)
(D:\miktex\tex/latex/babel-english\english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language73). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language73). Reported on input line 108.
))
(D:\miktex\tex/generic/babel/locale/en\babel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
)
(D:\miktex\tex/generic/babel/locale/fr\babel-french.tex
Package babel Info: Importing font and identification data for french
(babel)             from babel-fr.ini. Reported on input line 11.
)
(D:\miktex\tex/latex/carlisle\scalefnt.sty)
(D:\miktex\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (D:\miktex\tex/latex/psnfss\mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup4
\symitalic=\mathgroup5
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
) (D:\miktex\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(D:\miktex\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(D:\miktex\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (D:\miktex\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen151
\Gm@wd@mp=\dimen152
\Gm@odd@mp=\dimen153
\Gm@even@mp=\dimen154
\Gm@layoutwidth=\dimen155
\Gm@layoutheight=\dimen156
\Gm@layouthoffset=\dimen157
\Gm@layoutvoffset=\dimen158
\Gm@dimlist=\toks21
 (D:\miktex\tex/latex/geometry\geometry.cfg))
(D:\miktex\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(D:\miktex\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(D:\miktex\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen159
\Gin@req@width=\dimen160
)
(D:\miktex\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(D:\miktex\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(D:\miktex\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(D:\miktex\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip50
\f@nch@offset@elh=\skip51
\f@nch@offset@erh=\skip52
\f@nch@offset@olh=\skip53
\f@nch@offset@orh=\skip54
\f@nch@offset@elf=\skip55
\f@nch@offset@erf=\skip56
\f@nch@offset@olf=\skip57
\f@nch@offset@orf=\skip58
\f@nch@height=\skip59
\f@nch@footalignment=\skip60
\f@nch@widthL=\skip61
\f@nch@widthC=\skip62
\f@nch@widthR=\skip63
\@temptokenb=\toks22
)
(D:\miktex\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box51
\beforetitleunit=\skip64
\aftertitleunit=\skip65
\ttl@plus=\dimen161
\ttl@minus=\dimen162
\ttl@toksa=\toks23
\titlewidth=\dimen163
\titlewidthlast=\dimen164
\titlewidthfirst=\dimen165
)
(D:\miktex\tex/latex/enumitem\enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip66
\enit@outerparindent=\dimen166
\enit@toks=\toks24
\enit@inbox=\box52
\enit@count@id=\count273
\enitdp@description=\count274
)
(D:\miktex\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen167
\lightrulewidth=\dimen168
\cmidrulewidth=\dimen169
\belowrulesep=\dimen170
\belowbottomsep=\dimen171
\aboverulesep=\dimen172
\abovetopsep=\dimen173
\cmidrulesep=\dimen174
\cmidrulekern=\dimen175
\defaultaddspace=\dimen176
\@cmidla=\count275
\@cmidlb=\count276
\@aboverulesep=\dimen177
\@belowrulesep=\dimen178
\@thisruleclass=\count277
\@lastruleclass=\count278
\@thisrulewidth=\dimen179
)
(D:\miktex\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen180
\ar@mcellbox=\box53
\extrarowheight=\dimen181
\NC@list=\toks25
\extratabsurround=\skip67
\backup@length=\skip68
\ar@cellbox=\box54
) (D:\miktex\tex/latex/tools\multicol.sty
Package: multicol 2023/03/30 v1.9f multicolumn formatting (FMi)
\c@tracingmulticols=\count279
\mult@box=\box55
\multicol@leftmargin=\dimen182
\c@unbalance=\count280
\c@collectmore=\count281
\doublecol@number=\count282
\multicoltolerance=\count283
\multicolpretolerance=\count284
\full@width=\dimen183
\page@free=\dimen184
\premulticols=\dimen185
\postmulticols=\dimen186
\multicolsep=\skip69
\multicolbaselineskip=\skip70
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen187
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count285
\c@columnbadness=\count286
\c@finalcolumnbadness=\count287
\last@try=\dimen188
\multicolovershoot=\dimen189
\multicolundershoot=\dimen190
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count288
)
(D:\miktex\tex/latex/pgf/frontendlayer\tikz.sty
(D:\miktex\tex/latex/pgf/basiclayer\pgf.sty
(D:\miktex\tex/latex/pgf/utilities\pgfrcs.sty
(D:\miktex\tex/generic/pgf/utilities\pgfutil-common.tex
\pgfutil@everybye=\toks26
\pgfutil@tempdima=\dimen191
\pgfutil@tempdimb=\dimen192
)
(D:\miktex\tex/generic/pgf/utilities\pgfutil-latex.def
\pgfutil@abb=\box101
)
(D:\miktex\tex/generic/pgf/utilities\pgfrcs.code.tex
(D:\miktex\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/latex/pgf/basiclayer\pgfcore.sty
(D:\miktex\tex/latex/pgf/systemlayer\pgfsys.sty
(D:\miktex\tex/generic/pgf/systemlayer\pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/utilities\pgfkeys.code.tex
\pgfkeys@pathtoks=\toks27
\pgfkeys@temptoks=\toks28

(D:\miktex\tex/generic/pgf/utilities\pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks29
))
\pgf@x=\dimen193
\pgf@y=\dimen194
\pgf@xa=\dimen195
\pgf@ya=\dimen196
\pgf@xb=\dimen197
\pgf@yb=\dimen198
\pgf@xc=\dimen199
\pgf@yc=\dimen256
\pgf@xd=\dimen257
\pgf@yd=\dimen258
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count289
\c@pgf@countb=\count290
\c@pgf@countc=\count291
\c@pgf@countd=\count292
\t@pgf@toka=\toks30
\t@pgf@tokb=\toks31
\t@pgf@tokc=\toks32
\pgf@sys@id@count=\count293

(D:\miktex\tex/generic/pgf/systemlayer\pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/systemlayer\pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count294
\pgfsyssoftpath@bigbuffer@items=\count295
)
(D:\miktex\tex/generic/pgf/systemlayer\pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/generic/pgf/basiclayer\pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/math\pgfmath.code.tex
(D:\miktex\tex/generic/pgf/math\pgfmathutil.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathparser.code.tex
\pgfmath@dimen=\dimen259
\pgfmath@count=\count296
\pgfmath@box=\box102
\pgfmath@toks=\toks33
\pgfmath@stack@operand=\toks34
\pgfmath@stack@operation=\toks35
)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.basic.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.trigonometric.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.random.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.comparison.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.base.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.round.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.misc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.integerarithmetics.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathcalc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count297
))
(D:\miktex\tex/generic/pgf/math\pgfint.code.tex)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen260
\pgf@picmaxx=\dimen261
\pgf@picminy=\dimen262
\pgf@picmaxy=\dimen263
\pgf@pathminx=\dimen264
\pgf@pathmaxx=\dimen265
\pgf@pathminy=\dimen266
\pgf@pathmaxy=\dimen267
\pgf@xx=\dimen268
\pgf@xy=\dimen269
\pgf@yx=\dimen270
\pgf@yy=\dimen271
\pgf@zx=\dimen272
\pgf@zy=\dimen273
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen274
\pgf@path@lasty=\dimen275
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen276
\pgf@shorten@start@additional=\dimen277
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box103
\pgf@hbox=\box104
\pgf@layerbox@main=\box105
\pgf@picture@serial@count=\count298
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen278
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen279
\pgf@pt@y=\dimen280
\pgf@pt@temp=\dimen281
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen282
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen283
\pgf@sys@shading@range@num=\count299
\pgf@shadingcount=\count300
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box106
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/modules\pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box107
)
(D:\miktex\tex/generic/pgf/modules\pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen284
\pgf@nodesepend=\dimen285
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/latex/pgf/utilities\pgffor.sty
(D:\miktex\tex/latex/pgf/utilities\pgfkeys.sty
(D:\miktex\tex/generic/pgf/utilities\pgfkeys.code.tex))
(D:\miktex\tex/latex/pgf/math\pgfmath.sty
(D:\miktex\tex/generic/pgf/math\pgfmath.code.tex))
(D:\miktex\tex/generic/pgf/utilities\pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen286
\pgffor@skip=\dimen287
\pgffor@stack=\toks36
\pgffor@toks=\toks37
))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/libraries\pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count301
\pgfplotmarksize=\dimen288
)
\tikz@lastx=\dimen289
\tikz@lasty=\dimen290
\tikz@lastxsaved=\dimen291
\tikz@lastysaved=\dimen292
\tikz@lastmovetox=\dimen293
\tikz@lastmovetoy=\dimen294
\tikzleveldistance=\dimen295
\tikzsiblingdistance=\dimen296
\tikz@figbox=\box108
\tikz@figbox@bg=\box109
\tikz@tempbox=\box110
\tikz@tempbox@bg=\box111
\tikztreelevel=\count302
\tikznumberofchildren=\count303
\tikznumberofcurrentchild=\count304
\tikz@fig@count=\count305

(D:\miktex\tex/generic/pgf/modules\pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count306
\pgfmatrixcurrentcolumn=\count307
\pgf@matrix@numberofcolumns=\count308
)
\tikz@expandcount=\count309

(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarytopaths.code
.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (D:\miktex\tex/latex/pgfplots\pgfplots.sty
(D:\miktex\tex/generic/pgfplots\pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(D:\miktex\tex/generic/pgfplots\pgfplots.code.tex
(D:\miktex\tex/generic/pgfplots\pgfplotscore.code.tex
\t@pgfplots@toka=\toks38
\t@pgfplots@tokb=\toks39
\t@pgfplots@tokc=\toks40
\pgfplots@tmpa=\dimen297
\c@pgfplots@coordindex=\count310
\c@pgfplots@scanlineindex=\count311

(D:\miktex\tex/generic/pgfplots/sys\pgfplotssysgeneric.code.tex))
(D:\miktex\tex/generic/pgfplots/libs\pgfplotslibrary.code.tex)
(D:\miktex\tex/generic/pgfplots/oldpgfcompatib\pgfplotsoldpgfsupp_loader.code.t
ex (D:\miktex\tex/generic/pgf/libraries\pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks41
\t@pgf@tokb=\toks42
\t@pgf@tokc=\toks43

(D:\miktex\tex/generic/pgfplots/oldpgfcompatib\pgfplotsoldpgfsupp_pgfutil-commo
n-lists.tex)) (D:\miktex\tex/generic/pgfplots/util\pgfplotsutil.code.tex
(D:\miktex\tex/generic/pgfplots/liststructure\pgfplotsliststructure.code.tex)
(D:\miktex\tex/generic/pgfplots/liststructure\pgfplotsliststructureext.code.tex
) (D:\miktex\tex/generic/pgfplots/liststructure\pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count312
)
(D:\miktex\tex/generic/pgfplots/liststructure\pgfplotsmatrix.code.tex)
(D:\miktex\tex/generic/pgfplots/numtable\pgfplotstableshared.code.tex
\c@pgfplotstable@counta=\count313
\t@pgfplotstable@a=\toks44
)
(D:\miktex\tex/generic/pgfplots/liststructure\pgfplotsdeque.code.tex)
(D:\miktex\tex/generic/pgfplots/util\pgfplotsbinary.code.tex
(D:\miktex\tex/generic/pgfplots/util\pgfplotsbinary.data.code.tex))
(D:\miktex\tex/generic/pgfplots/util\pgfplotsutil.verb.code.tex)
(D:\miktex\tex/generic/pgfplots/libs\pgflibrarypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count314

(D:\miktex\tex/generic/pgfplots/sys\pgflibrarypgfplots.surfshading.pgfsys-pdfte
x.def))) (D:\miktex\tex/generic/pgfplots/util\pgfplotscolormap.code.tex
(D:\miktex\tex/generic/pgfplots/util\pgfplotscolor.code.tex))
(D:\miktex\tex/generic/pgfplots\pgfplotsstackedplots.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplotsplothandlers.code.tex
(D:\miktex\tex/generic/pgfplots\pgfplotsmeshplothandler.code.tex
(D:\miktex\tex/generic/pgfplots\pgfplotsmeshplotimage.code.tex)))
(D:\miktex\tex/generic/pgfplots\pgfplots.scaling.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplotscoordprocessing.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplots.errorbars.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplots.markers.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplotsticks.code.tex)
(D:\miktex\tex/generic/pgfplots\pgfplots.paths.code.tex)
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarydecorations.
code.tex (D:\miktex\tex/generic/pgf/modules\pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen298
\pgfdecoratedremainingdistance=\dimen299
\pgfdecoratedinputsegmentcompleteddistance=\dimen300
\pgfdecoratedinputsegmentremainingdistance=\dimen301
\pgf@decorate@distancetomove=\dimen302
\pgf@decorate@repeatstate=\count315
\pgfdecorationsegmentamplitude=\dimen303
\pgfdecorationsegmentlength=\dimen304
)
\tikz@lib@dec@box=\box112
)
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarydecorations.
pathmorphing.code.tex
(D:\miktex\tex/generic/pgf/libraries/decorations\pgflibrarydecorations.pathmorp
hing.code.tex))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarydecorations.
pathreplacing.code.tex
(D:\miktex\tex/generic/pgf/libraries/decorations\pgflibrarydecorations.pathrepl
acing.code.tex))
(D:\miktex\tex/generic/pgfplots/libs\tikzlibrarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count316
\pgfplots@xmin@reg=\dimen305
\pgfplots@xmax@reg=\dimen306
\pgfplots@ymin@reg=\dimen307
\pgfplots@ymax@reg=\dimen308
\pgfplots@zmin@reg=\dimen309
\pgfplots@zmax@reg=\dimen310
)
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibraryplotmarks.co
de.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (D:\miktex\tex/generic/pgf/libraries\pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip71

For additional information on amsmath, use the `?' option.
(D:\miktex\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(D:\miktex\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks45
\ex@=\dimen311
))
(D:\miktex\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen312
)
(D:\miktex\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count317
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count318
\leftroot@=\count319
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count320
\DOTSCASE@=\count321
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box113
\strutbox@=\box114
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen313
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count322
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count323
\dotsspace@=\muskip16
\c@parentequation=\count324
\dspbrk@lvl=\count325
\tag@help=\toks46
\row@=\count326
\column@=\count327
\maxfields@=\count328
\andhelp@=\toks47
\eqnshift@=\dimen314
\alignsep@=\dimen315
\tagshift@=\dimen316
\tagwidth@=\dimen317
\totwidth@=\dimen318
\lineht@=\dimen319
\@envbody=\toks48
\multlinegap=\skip72
\multlinetaggap=\skip73
\mathdisplay@stack=\toks49
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(D:\miktex\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX

(D:\miktex\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(D:\miktex\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(D:\miktex\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(D:\miktex\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(D:\miktex\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(D:\miktex\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(D:\miktex\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(D:\miktex\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(D:\miktex\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(D:\miktex\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(D:\miktex\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(D:\miktex\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(D:\miktex\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count329
)
(D:\miktex\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count330
)
\@linkdim=\dimen320
\Hy@linkcounter=\count331
\Hy@pagecounter=\count332

(D:\miktex\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(D:\miktex\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count333

(D:\miktex\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4448.
\c@Hy@tempcnt=\count334
 (D:\miktex\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen321

(D:\miktex\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(D:\miktex\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count335
\Field@Width=\dimen322
\Fld@charsize=\dimen323
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring OFF on input line 6085.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.

(D:\miktex\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count336
\c@Item=\count337
\c@Hfootnote=\count338
)
Package hyperref Info: Driver (autodetected): hpdftex.

(D:\miktex\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2023-11-26 v7.01g Hyperref driver for pdfTeX

(D:\miktex\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count339
\c@bookmark@seq@number=\count340

(D:\miktex\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(D:\miktex\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip74
)
(D:\miktex\tex/latex/tcolorbox\tcolorbox.sty
Package: tcolorbox 2024/01/10 version 6.2.0 text color boxes

(D:\miktex\tex/latex/tools\verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks50
\verbatim@line=\toks51
\verbatim@in@stream=\read4
)
(D:\miktex\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(D:\miktex\tex/latex/trimspaces\trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box115
\tcb@upperbox=\box116
\tcb@lowerbox=\box117
\tcb@phantombox=\box118
\c@tcbbreakpart=\count341
\c@tcblayer=\count342
\c@tcolorbox@number=\count343
\l__tcobox_tmpa_box=\box119
\l__tcobox_tmpa_dim=\dimen324
\tcb@temp=\box120
\tcb@temp=\box121
\tcb@temp=\box122
\tcb@temp=\box123
)
(D:\miktex\tex/latex/shadowtext\shadowtext.sty
Package: shadowtext 2012/05/07 v0.3 Shadow Text
\st@shadowoffsetx=\skip75
\st@shadowoffsety=\skip76
\st@temp@width=\skip77
\st@temp@height=\skip78
\st@pic@width=\skip79
\st@pic@height=\skip80
)
(D:\miktex\tex/latex/fontawesome\fontawesome.sty
Package: fontawesome 2016/05/15 v4.6.3.1 font awesome icons

(D:\miktex\tex/generic/iftex\ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
)
(D:\miktex\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
(D:\miktex\tex/latex/fontawesome\fontawesomesymbols-generic.tex)
(D:\miktex\tex/latex/fontawesome\fontawesomesymbols-pdftex.tex))
(D:\miktex\tex/latex/colortbl\colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks52
\minrowclearance=\skip81
\rownum=\count344
)
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 56
.

(D:\miktex\tex/latex/psnfss\t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
)
(D:\miktex\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count345
\l__pdf_internal_box=\box124
) (main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 56.
LaTeX Font Info:    ... okay on input line 56.
LaTeX Info: Redefining \degres on input line 56.
LaTeX Info: Redefining \up on input line 56.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(85.35826pt, 674.33032pt, 85.35826pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=674.33032pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-23.91173pt
* \headheight=14.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(D:\miktex\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count346
\scratchdimen=\dimen325
\scratchbox=\box125
\nofMPsegments=\count347
\nofMParguments=\count348
\everyMPshowfont=\toks53
\MPscratchCnt=\count349
\MPscratchDim=\dimen326
\MPnumerator=\count350
\makeMPintoPDFobject=\count351
\everyMPtoPDFconversion=\toks54
) (D:\miktex\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(D:\miktex\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package pgfplots notification 'compat/show suggested version=true': document ha
s been generated with the most recent feature set (\pgfplotsset{compat=1.18}).

Package hyperref Info: Link coloring OFF on input line 56.
(main.out) (main.out)
\@outlinefile=\write4
\openout4 = `main.out'.

LaTeX Font Info:    Trying to load font information for OT1+ztmcm on input line
 100.
 (D:\miktex\tex/latex/psnfss\ot1ztmcm.fd
File: ot1ztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OT1/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OML+ztmcm on input line
 100.

(D:\miktex\tex/latex/psnfss\omlztmcm.fd
File: omlztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OML/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMS+ztmcm on input line
 100.

(D:\miktex\tex/latex/psnfss\omsztmcm.fd
File: omsztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMS/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMX+ztmcm on input line
 100.

(D:\miktex\tex/latex/psnfss\omxztmcm.fd
File: omxztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMX/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 1
00.
 (D:\miktex\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10.95> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 100.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 100.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 100.
[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{D:/miktex/fon
ts/enc/dvips/base/8r.enc}] (main.toc)
\tf@toc=\write5
\openout5 = `main.toc'.


pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.137 \newpage
               [1]
<darden_pm_pictures/saray project.png, id=182, 1445.4pt x 813.0375pt>
File: darden_pm_pictures/saray project.png Graphic file (type png)
<use darden_pm_pictures/saray project.png>
Package pdftex.def Info: darden_pm_pictures/saray project.png  used on input li
ne 153.
(pdftex.def)             Requested size: 256.05855pt x 144.03293pt.
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 1
70.
 (D:\miktex\tex/latex/psnfss\ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
) [2 <./darden_pm_pictures/saray project.png>]
Overfull \hbox (76.69629pt too wide) in paragraph at lines 220--231
[][] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 243--244
\T1/ptm/m/it/10.95 Reduces lo-ca-tion risk
 []


Overfull \vbox (20.04768pt too high) has occurred while \output is active []


Overfull \vbox (20.04768pt too high) has occurred while \output is active []


[3]
<darden_pm_pictures/main_studio.jpg, id=193, 1606.0pt x 1204.5pt>
File: darden_pm_pictures/main_studio.jpg Graphic file (type jpg)
<use darden_pm_pictures/main_studio.jpg>
Package pdftex.def Info: darden_pm_pictures/main_studio.jpg  used on input line
 281.
(pdftex.def)             Requested size: 303.47679pt x 227.60759pt.
 [4]
Overfull \hbox (79.62627pt too wide) in paragraph at lines 314--327
[][] 
 []

[5 <./darden_pm_pictures/main_studio.jpg>]
Underfull \hbox (badness 7576) in paragraph at lines 336--337
[]\T1/ptm/b/n/10.95 Property Man-age-ment: \T1/ptm/m/n/10.95 16,800
 []


Underfull \hbox (badness 10000) in paragraph at lines 348--349
[]\T1/ptm/b/n/10.95 Less: Op-er-at-ing Ex-penses:
 []

[6]
<darden_pm_pictures/saray project2.jpg, id=208, 748.7975pt x 381.425pt>
File: darden_pm_pictures/saray project2.jpg Graphic file (type jpg)
<use darden_pm_pictures/saray project2.jpg>
Package pdftex.def Info: darden_pm_pictures/saray project2.jpg  used on input l
ine 412.
(pdftex.def)             Requested size: 312.9846pt x 159.42915pt.
 [7 <./darden_pm_pictures/saray project2.jpg>]
Underfull \hbox (badness 10000) in paragraph at lines 458--459
[]|\T1/ptm/m/n/10.95 Premium lo-ca-tions main-tain
 []

[8]
Overfull \hbox (0.74716pt too wide) in paragraph at lines 545--566
[][] 
 []

[9]
Underfull \hbox (badness 5050) in paragraph at lines 575--576
[]\T1/ptm/b/n/10.95 Prime Lo-ca-tions: \T1/ptm/m/n/10.95 Prop-er-ties in
 []

[10]
Overfull \hbox (1.11964pt too wide) in paragraph at lines 628--629
[]\T1/ptm/m/n/10.95 Darden Prop-erty & Man-age-ment presents an ex-cep-tional i
n-vest-ment op-por-tu-nity that com-bines strate-
 []


Underfull \hbox (badness 7925) in paragraph at lines 647--648
[]\T1/ptm/b/n/10.95 Conservative Cap-i-tal Struc-ture:
 []


Underfull \hbox (badness 10000) in paragraph at lines 649--650
[]\T1/ptm/b/n/10.95 Exceptional Growth Po-ten-tial:
 []


Underfull \hbox (badness 10000) in paragraph at lines 649--650
\T1/ptm/m/n/10.95 Strate-gic po-si-tion-ing in high-
 []


Overfull \vbox (240.50989pt too high) has occurred while \output is active []


Overfull \vbox (240.50989pt too high) has occurred while \output is active []


[11]
Underfull \hbox (badness 6268) in paragraph at lines 660--661
[]\T1/ptm/m/n/10.95 Conservative 50%
 []


Underfull \hbox (badness 7832) in paragraph at lines 663--664
[]\T1/ptm/m/n/10.95 Professional man-
 []


Underfull \hbox (badness 10000) in paragraph at lines 671--672
[]\T1/ptm/m/n/10.95 Stable in-come
 []


Underfull \hbox (badness 10000) in paragraph at lines 672--673
[]\T1/ptm/m/n/10.95 Market lead-er-ship
 []


Underfull \hbox (badness 7649) in paragraph at lines 673--674
[]\T1/ptm/m/n/10.95 Professional over-
 []


Underfull \hbox (badness 10000) in paragraph at lines 681--682
[]\T1/ptm/m/n/10.95 Moroccan mar-ket
 []


Underfull \hbox (badness 10000) in paragraph at lines 682--683
[]\T1/ptm/m/n/10.95 Professional part-
 []


Underfull \hbox (badness 10000) in paragraph at lines 683--684
[]\T1/ptm/m/n/10.95 Risk-adjusted
 []

[12] [13] (main.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********


Package rerunfilecheck Warning: File `main.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `main.out':
(rerunfilecheck)             Before: C7224CA4ADD65BB8CB6461343A1EC688;6797
(rerunfilecheck)             After:  D7DC5694EB9F040F256A63B181659CF3;6495.
 ) 
Here is how much of TeX's memory you used:
 37875 strings out of 474486
 857629 string characters out of 5760170
 1951542 words of memory out of 5000000
 59520 multiletter control sequences out of 15000+600000
 628105 words of font info for 103 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 84i,17n,87p,727b,2208s stack positions out of 10000i,1000n,20000p,200000b,200000s

pdfTeX warning (dest): name{subsection.3.2} has been referenced but does not ex
ist, replaced by a fixed one

<D:/miktex/fonts/type1/public/amsfonts/cm/cmsy10.pfb><D:/miktex/fonts/type1/urw
/times/utmb8a.pfb><D:/miktex/fonts/type1/urw/times/utmr8a.pfb><D:/miktex/fonts/
type1/urw/times/utmri8a.pfb>
Output written on main.pdf (14 pages, 3716748 bytes).
PDF statistics:
 270 PDF objects out of 1000 (max. 8388607)
 47 named destinations out of 1000 (max. 500000)
 292 words of extra memory for PDF output out of 10000 (max. 10000000)

